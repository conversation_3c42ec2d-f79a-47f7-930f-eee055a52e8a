// 超复杂C++语法堆排序实现
// 包含: 模板元编程、SFINAE、完美转发、lambda、变参模板、CRTP等

#include <iostream>
#include <vector>
#include <array>
#include <type_traits>
#include <functional>
#include <memory>
#include <utility>
#include <algorithm>
#include <iterator>
#include <chrono>
#include <numeric>

// 命名空间别名和using声明
namespace heap_sort_ns = std;

// C++17之前的概念模拟 - 使用SFINAE
template<typename T>
struct is_comparable {
private:
    template<typename U>
    static auto test_less(int) -> decltype(std::declval<U>() < std::declval<U>(), std::true_type{});
    template<typename>
    static std::false_type test_less(...);

    template<typename U>
    static auto test_greater(int) -> decltype(std::declval<U>() > std::declval<U>(), std::true_type{});
    template<typename>
    static std::false_type test_greater(...);

public:
    static constexpr bool value = decltype(test_less<T>(0))::value &&
                                 decltype(test_greater<T>(0))::value;
};

template<typename T>
constexpr bool is_comparable_v = is_comparable<T>::value;

template<typename T>
struct is_swappable {
private:
    template<typename U>
    static auto test(int) -> decltype(std::swap(std::declval<U&>(), std::declval<U&>()), std::true_type{});
    template<typename>
    static std::false_type test(...);

public:
    static constexpr bool value = decltype(test<T>(0))::value;
};

template<typename T>
constexpr bool is_swappable_v = is_swappable<T>::value;

template<typename Container>
struct is_random_access_container {
private:
    template<typename C>
    static auto test(int) -> decltype(
        typename C::value_type{},
        typename C::iterator{},
        std::declval<C>().begin(),
        std::declval<C>().end(),
        std::declval<C>().size(),
        std::declval<C>()[0],
        std::true_type{}
    );
    template<typename>
    static std::false_type test(...);

public:
    static constexpr bool value = decltype(test<Container>(0))::value;
};

template<typename Container>
constexpr bool is_random_access_container_v = is_random_access_container<Container>::value;

// 模板元编程：编译时计算
template<std::size_t N>
struct CompileTimeLog2 {
    static constexpr std::size_t value = 1 + CompileTimeLog2<N / 2>::value;
};

template<>
struct CompileTimeLog2<1> {
    static constexpr std::size_t value = 0;
};

template<>
struct CompileTimeLog2<0> {
    static constexpr std::size_t value = 0;
};5

// 变参模板和递归展开 (C++14兼容)
template<typename T>
constexpr T sum_all(T&& t) {
    return std::forward<T>(t);
}

template<typename T, typename... Args>
constexpr auto sum_all(T&& t, Args&&... args) {
    return std::forward<T>(t) + sum_all(std::forward<Args>(args)...);
}

// SFINAE 和 enable_if
template<typename T>
typename std::enable_if_t<std::is_arithmetic<T>::value, T>
constexpr max_of_three(T a, T b, T c) {
    return std::max({a, b, c});
}

// CRTP (Curiously Recurring Template Pattern)
template<typename Derived>
class HeapSortBase {
public:
    template<typename Container>
    void sort(Container& container) {
        static_cast<Derived*>(this)->sort_impl(container);
    }

protected:
    ~HeapSortBase() = default;
};

// 策略模式与模板特化
template<typename ComparePolicy>
struct HeapifyStrategy;

template<>
struct HeapifyStrategy<std::less<void>> {
    template<typename Iterator, typename Compare>
    static void heapify(Iterator first, Iterator last, Iterator pos, Compare comp) {
        auto size = std::distance(first, last);
        auto index = std::distance(first, pos);
        auto largest = index;
        auto left = 2 * index + 1;
        auto right = 2 * index + 2;

        if (left < size && comp(*(first + largest), *(first + left))) {
            largest = left;
        }

        if (right < size && comp(*(first + largest), *(first + right))) {
            largest = right;
        }

        if (largest != index) {
            std::iter_swap(first + index, first + largest);
            heapify(first, last, first + largest, comp);
        }
    }
};

// 主要的堆排序类，使用CRTP
template<typename ComparePolicy = std::less<void>>
class AdvancedHeapSort : public HeapSortBase<AdvancedHeapSort<ComparePolicy>> {
private:
    ComparePolicy comp_;

    // 完美转发和通用引用
    template<typename Container, typename Compare>
    void heapify_impl(Container& container, std::size_t n, std::size_t i, Compare&& comp) {
        auto largest = i;
        auto left = 2 * i + 1;
        auto right = 2 * i + 2;

        // 使用lambda表达式和捕获
        auto compare_and_update = [&](std::size_t child) {
            if (child < n && std::forward<Compare>(comp)(container[largest], container[child])) {
                largest = child;
            }
        };

        compare_and_update(left);
        compare_and_update(right);

        if (largest != i) {
            // 使用std::exchange进行原子交换
            std::swap(container[i], container[largest]);
            heapify_impl(container, n, largest, std::forward<Compare>(comp));
        }
    }

public:
    // 构造函数模板和完美转发
    template<typename... Args>
    explicit AdvancedHeapSort(Args&&... args)
        : comp_(std::forward<Args>(args)...) {}

    // 主排序函数，使用SFINAE约束
    template<typename Container>
    typename std::enable_if_t<
        is_random_access_container_v<Container> &&
        is_comparable_v<typename Container::value_type> &&
        is_swappable_v<typename Container::value_type>
    > sort_impl(Container& container) {
        const auto n = container.size();
        if (n <= 1) return;

        // 构建最大堆
        for (auto i = n / 2; i > 0; --i) {
            heapify_impl(container, n, i - 1, comp_);
        }

        // 逐个提取元素
        for (auto i = n - 1; i > 0; --i) {
            std::swap(container[0], container[i]);
            heapify_impl(container, i, 0, comp_);
        }
    }

    // 函数对象重载
    template<typename Container>
    auto operator()(Container& container) -> decltype(auto) {
        sort_impl(container);
        return *this;
    }
};

// 工厂函数模板
template<typename ComparePolicy = std::less<void>, typename... Args>
auto make_heap_sorter(Args&&... args) {
    return std::make_unique<AdvancedHeapSort<ComparePolicy>>(std::forward<Args>(args)...);
}

// 类型别名模板
template<typename T>
using heap_sorter_t = AdvancedHeapSort<T>;

// 变量模板 (C++14)
template<typename T>
constexpr bool is_sortable_v = is_comparable_v<T> && is_swappable_v<T>;

// 自定义分配器示例
template<typename T>
class CustomAllocator {
public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    template<typename U>
    struct rebind {
        using other = CustomAllocator<U>;
    };

    CustomAllocator() noexcept = default;

    template<typename U>
    CustomAllocator(const CustomAllocator<U>&) noexcept {}

    pointer allocate(size_type n) {
        return static_cast<pointer>(std::malloc(n * sizeof(T)));
    }

    void deallocate(pointer p, size_type) noexcept {
        std::free(p);
    }

    template<typename U, typename... Args>
    void construct(U* p, Args&&... args) {
        new(p) U(std::forward<Args>(args)...);
    }

    template<typename U>
    void destroy(U* p) {
        p->~U();
    }
};

// 特化的vector类型
template<typename T>
using custom_vector = std::vector<T, CustomAllocator<T>>;

int main() {
    // 使用各种复杂的C++特性

    // 1. 基本用法
    std::vector<int> arr1{12, 11, 13, 5, 6, 7, 42, 1, 99, 3};
    auto sorter1 = AdvancedHeapSort<std::less<void>>{};
    sorter1.sort(arr1);

    std::cout << "基本排序结果: ";
    std::copy(arr1.begin(), arr1.end(), std::ostream_iterator<int>(std::cout, " "));
    std::cout << "\n";

    // 2. 使用自定义比较器和lambda
    std::array<double, 8> arr2{3.14, 2.71, 1.41, 1.73, 0.57, 2.23, 1.61, 0.69};
    auto custom_comp = [](const auto& a, const auto& b) { return a > b; }; // 降序
    auto sorter2 = AdvancedHeapSort<decltype(custom_comp)>{custom_comp};
    sorter2.sort(arr2);

    std::cout << "自定义比较器排序结果: ";
    for (const auto& val : arr2) {
        std::cout << val << " ";
    }
    std::cout << "\n";

    // 3. 使用工厂函数和智能指针
    custom_vector<int> arr3{100, 50, 75, 25, 125, 10, 200};
    auto sorter3 = make_heap_sorter<std::greater<void>>();
    (*sorter3)(arr3);

    std::cout << "工厂函数创建的排序器结果: ";
    std::copy(arr3.begin(), arr3.end(), std::ostream_iterator<int>(std::cout, " "));
    std::cout << "\n";

    // 4. 编译时计算展示
    constexpr auto log_val = CompileTimeLog2<1024>::value;
    std::cout << "编译时计算 log2(1024) = " << log_val << "\n";

    // 5. 变参模板和折叠表达式
    auto sum = sum_all(1, 2, 3, 4, 5);
    std::cout << "变参模板求和结果: " << sum << "\n";

    // 6. 性能测试使用chrono5
    auto start = std::chrono::high_resolution_clock::now();

    std::vector<int> large_arr(10000);
    std::iota(large_arr.rbegin(), large_arr.rend(), 1); // 逆序填充

    auto large_sorter = AdvancedHeapSort<>{};
    large_sorter.sort(large_arr);

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    std::cout << "大数组排序耗时: " << duration.count() << " 微秒\n";
    std::cout << "前10个元素: ";
    std::copy_n(large_arr.begin(), 10, std::ostream_iterator<int>(std::cout, " "));
    std::cout << "\n";

    return 0;
}

